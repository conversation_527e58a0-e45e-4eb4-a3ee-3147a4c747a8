// Exportar todo desde los archivos individuales
export * from './geminiClient';
export * from './questionService';
export * from './flashcardGenerator';
export * from './testGenerator';
export * from './mindMapGenerator';
export * from './resumenGenerator';

// Importar tipos necesarios
import { type Documento } from '@/types';

// Definición de interfaces para compatibilidad
export interface PreguntaGenerada {
  pregunta: string;
  opciones: {
    a: string;
    b: string;
    c: string;
    d: string;
  };
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}

// Función adaptadora para compatibilidad con la interfaz anterior de flashcards
export async function generarFlashcards(
  peticion: string,
  contextosODocumentos: string[] | any[],
  cantidad: number = 10
): Promise<{ pregunta: string; respuesta: string }[]> {
  let documentos;

  // *** INICIO DE LA CORRECCIÓN ***
  // Verificar si recibimos documentos completos o solo strings
  if (Array.isArray(contextosODocumentos) && contextosODocumentos.length > 0) {
    if (typeof contextosODocumentos[0] === 'string') {
      // Es un array de strings (contextos), convertir al formato esperado
      documentos = contextosODocumentos.map((contenido, index) => ({
        titulo: `Documento ${index + 1}`,
        contenido
      }));
    } else {
      // Es un array de documentos completos, usar directamente
      documentos = contextosODocumentos;
    }
  } else {
    throw new Error('Se requieren contextos o documentos para generar flashcards');
  }
  // *** FIN DE LA CORRECCIÓN ***

  // Llamar a la función original con los documentos formateados y la petición como instrucción
  return await import('./flashcardGenerator').then(module =>
    module.generarFlashcards(documentos, cantidad, peticion)
  );
}

// Función adaptadora para compatibilidad con la interfaz anterior de mapas mentales
export async function generarMapaMental(
  peticion: string,
  contextosODocumentos: string[] | any[]
): Promise<any> {
  let documentos;

  // *** INICIO DE LA CORRECCIÓN ***
  // Verificar si recibimos documentos completos o solo strings
  if (Array.isArray(contextosODocumentos) && contextosODocumentos.length > 0) {
    if (typeof contextosODocumentos[0] === 'string') {
      // Es un array de strings (contextos), convertir al formato esperado
      documentos = contextosODocumentos.map((contenido, index) => ({
        titulo: `Documento ${index + 1}`,
        contenido
      }));
    } else {
      // Es un array de documentos completos, usar directamente
      documentos = contextosODocumentos;
    }
  } else {
    throw new Error('Se requieren contextos o documentos para generar mapa mental');
  }
  // *** FIN DE LA CORRECCIÓN ***

  // Llamar a la función original con los documentos formateados y la petición como instrucción
  return await import('./mindMapGenerator').then(module =>
    module.generarMapaMental(documentos, peticion)
  );
}

// Función adaptadora para compatibilidad con la interfaz anterior de tests
export async function generarTest(
  peticion: string,
  contextosODocumentos: string[] | any[],
  cantidad: number = 10
): Promise<PreguntaGenerada[]> {
  let documentos;

  // *** INICIO DE LA CORRECCIÓN ***
  // Verificar si recibimos documentos completos o solo strings
  if (Array.isArray(contextosODocumentos) && contextosODocumentos.length > 0) {
    if (typeof contextosODocumentos[0] === 'string') {
      // Es un array de strings (contextos), convertir al formato esperado
      documentos = contextosODocumentos.map((contenido, index) => ({
        titulo: `Documento ${index + 1}`,
        contenido
      }));
    } else {
      // Es un array de documentos completos, usar directamente
      documentos = contextosODocumentos;
    }
  } else {
    throw new Error('Se requieren contextos o documentos para generar el test');
  }
  // *** FIN DE LA CORRECCIÓN ***

  // Llamar a la función original con los documentos formateados y la petición como instrucción
  const result = await import('./testGenerator').then(module =>
    module.generarTest(documentos, cantidad, peticion)
  );

  // Convertir el formato de la respuesta al formato esperado por el componente
  return result.map(item => ({
    pregunta: item.pregunta,
    opciones: {
      a: item.opcion_a,
      b: item.opcion_b,
      c: item.opcion_c,
      d: item.opcion_d
    },
    respuesta_correcta: item.respuesta_correcta
  }));
}

// Función adaptadora para compatibilidad con la interfaz anterior de resúmenes
export async function generarResumen(
  documento: { titulo: string; contenido: string; categoria?: string; numero_tema?: number },
  instrucciones?: string,
  userId?: string
): Promise<string> {
  // Crear un objeto Documento completo con los campos requeridos
  const documentoCompleto: Documento = {
    id: `temp_${Date.now()}`,
    titulo: documento.titulo,
    contenido: documento.contenido,
    categoria: documento.categoria || 'General',
    numero_tema: documento.numero_tema || 1,
    creado_en: new Date().toISOString(),
    actualizado_en: new Date().toISOString(),
    user_id: 'temp_user'
  };

  return await import('./resumenGenerator').then(module =>
    module.generarResumen(documentoCompleto, instrucciones, userId)
  );
}
